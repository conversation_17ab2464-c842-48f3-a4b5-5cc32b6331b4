body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #f0f2f5;
    color: #333;
}

h1 {
    color: #1a237e;
}

.game-container {
    display: flex;
    gap: 30px;
    margin-top: 20px;
}

.board-wrapper {
    position: relative;
}

.board {
    width: 550px; /* Approximate width */
    height: 750px; /* Approximate height based on image aspect ratio */
    background-image: url('棋盘.jpeg');
    background-size: contain;
    background-repeat: no-repeat;
    position: relative;
    border: 2px solid #333;
}

.cell {
    position: absolute;
    width: 50px;  /* Hitbox size */
    height: 38px; /* Hitbox size */
    transform: translate(-50%, -50%);
    z-index: 2;
}

.piece {
    width: 48px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
    border: 2px solid;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    position: absolute;
    transform: translate(-50%, -50%);
    z-index: 3;
}

.piece.player {
    background-color: #3f51b5;
    color: white;
    border-color: #1a237e;
}

.piece.ai {
    background-color: #c62828;
    color: white;
    border-color: #8e0000;
}

/* 隐藏的AI棋子样式 */
.piece.ai.hidden-piece {
    background-color: #424242;
    color: #ffffff;
    border-color: #212121;
    font-size: 20px;
    font-weight: bold;
}

.piece.ai.hidden-piece:hover {
    background-color: #616161;
    transform: translate(-50%, -50%) scale(1.05);
}

.piece.selected {
    transform: translate(-50%, -50%) scale(1.1);
    box-shadow: 0 0 15px #ffeb3b;
    border-color: #ffeb3b;
    z-index: 5;
}

.cell.movable-hint::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: rgba(255, 235, 59, 0.7);
    z-index: 1;
}

/* 现代化按钮样式 */
.modern-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    padding: 12px 16px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    text-transform: none;
}

.modern-btn-primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.25);
}

.modern-btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.35);
}

.modern-btn-secondary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 6px rgba(16, 185, 129, 0.25);
}

.modern-btn-secondary:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
    box-shadow: 0 6px 12px rgba(16, 185, 129, 0.35);
}

.modern-btn:disabled {
    background: #9ca3af !important;
    color: #6b7280 !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

/* 状态面板样式 */
.status-panel {
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-waiting {
    background-color: #fef3c7;
    border-left-color: #f59e0b;
    color: #92400e;
}

.status-playing {
    background-color: #dbeafe;
    border-left-color: #3b82f6;
    color: #1e40af;
}

.status-win {
    background-color: #dcfce7;
    border-left-color: #22c55e;
    color: #15803d;
}

.status-lose {
    background-color: #fef2f2;
    border-left-color: #ef4444;
    color: #dc2626;
}

/* 被吃掉的棋子样式 */
.captured-piece {
    display: inline-block;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    margin: 2px;
    min-width: 24px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#player-captured .captured-piece {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: 1px solid #b91c1c;
}

#ai-captured .captured-piece {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border: 1px solid #1e40af;
}

/* 状态面板文字样式 */
.status-panel p {
    margin: 0;
    line-height: 1.4;
}

.status-panel p:first-child {
    font-size: 14px;
}

.status-panel p:last-child {
    font-size: 13px;
    opacity: 0.9;
}

/* 保持原有的棋盘相关样式不变 */
.captured-pieces {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    min-height: 30px;
}

/* 移除旧的 .captured-pieces .piece 样式，因为现在使用 .captured-piece */
