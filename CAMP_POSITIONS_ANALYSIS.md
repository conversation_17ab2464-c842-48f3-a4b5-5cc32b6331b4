# 营地位置分析和布局重新设计

## 营地位置（禁止放棋子）
根据 boardSchema，营地位置（代码2）在玩家区域（行6-11）的坐标是：
- **(7,1), (7,3)** - 第7行的营地
- **(8,2)** - 第8行的营地  
- **(9,1), (9,3)** - 第9行的营地

## 可用位置分析
玩家区域（行6-11）的可用位置：
- **第6行**: (6,0), (6,1), (6,2), (6,3), (6,4) - 全部可用
- **第7行**: (7,0), ~~(7,1)~~, (7,2), ~~(7,3)~~, (7,4) - 3个可用
- **第8行**: (8,0), ~~(8,1)~~, ~~(8,2)~~, ~~(8,3)~~, (8,4) - 2个可用（注意：(8,1)和(8,3)不是营地！）
- **第9行**: (9,0), ~~(9,1)~~, (9,2), ~~(9,3)~~, (9,4) - 3个可用
- **第10行**: (10,0), (10,1), (10,2), (10,3), (10,4) - 全部可用
- **第11行**: (11,0), (11,1), (11,2), (11,3), (11,4) - 全部可用

等等！我需要重新检查 boardSchema：

```
[1, 1, 2, 1, 1], // Row 8, Camp
```

这意味着第8行只有(8,2)是营地，(8,1)和(8,3)是普通位置！

让我重新分析：

## 正确的营地位置
- **(7,1), (7,3)** - 营地
- **(8,2)** - 营地  
- **(9,1), (9,3)** - 营地

## 当前布局中的错误
### Layout1 ❌
- 营长在[8,1], [8,3] - **[8,1]和[8,3]不是营地！我搞错了**

### Layout2 ❌  
- 旅长在[8,1] - **[8,1]不是营地**
- 团长在[9,2] - **[9,2]不是营地**

### Layout3 ❌
- 师长在[9,1], [9,3] - **这些是营地**
- 旅长在[8,1], [8,3] - **这些不是营地**
- 团长在[7,1], [7,3] - **这些是营地**

### Layout4 ❌
- 师长在[9,1], [9,3] - **这些是营地**
- 旅长在[8,1], [8,3] - **这些不是营地**
- 团长在[7,1], [7,3] - **这些是营地**

### Layout5 ❌
- 师长在[9,1], [9,3] - **这些是营地**
- 旅长在[8,1], [8,3] - **这些不是营地**
- 团长在[7,1], [7,3] - **这些是营地**

## 需要修正的位置
只需要修正放在以下营地位置的棋子：
- **(7,1), (7,3)** 
- **(8,2)**
- **(9,1), (9,3)**

## 重新设计的布局

### Layout1 - 经典防守阵 ✅
当前布局基本正确，只需要微调。

### Layout2 - 积极进攻阵 ✅  
当前布局基本正确，只需要微调。

### Layout3 - 平衡发展阵 ❌
需要移动：
- 师长从[9,1], [9,3] → [8,1], [8,3]
- 团长从[7,1], [7,3] → [7,0], [7,4]

### Layout4 - 工兵突击阵 ❌
需要移动：
- 师长从[9,1], [9,3] → [8,1], [8,3]  
- 团长从[7,1], [7,3] → [7,0], [7,4]

### Layout5 - 铁桶防守阵 ❌
需要移动：
- 师长从[9,1], [9,3] → [8,1], [8,3]
- 团长从[7,1], [7,3] → [7,0], [7,4]
