# 大本营斜向移动规则修复

## 问题描述
之前的实现中，大本营（HQ）中的棋子可以进行斜向移动，这违反了军旗游戏的规则。根据正确的军旗规则，大本营中的棋子只能进行直线移动（上下左右），不能斜向移动。

## 问题原因
1. **移动规则逻辑错误**: 在`getValidMoves`函数中，代码允许从营地（camp）进行斜向移动，但没有区分营地和大本营
2. **显式对角连接错误**: `explicitDiagonalConnections`中包含了从大本营出发的斜向连接

## 修复内容

### 1. 修复移动规则逻辑
**位置**: `game.js` 第275-286行

**修复前**:
```javascript
// Diagonal moves: based on explicit connections or if starting from a camp
for (const [dr, dc] of diagonalDirections) {
    const nr = r + dr;
    const nc = c + dc;
    if (isValid(nr, nc)) {
        const connectionKey = `${r},${c}-${nr},${nc}`;
        if (isCamp(r, c) || explicitDiagonalConnections.has(connectionKey)) {
            addMove({ r: nr, c: nc });
        }
    }
}
```

**修复后**:
```javascript
// Diagonal moves: based on explicit connections or if starting from a camp (but NOT from HQ)
for (const [dr, dc] of diagonalDirections) {
    const nr = r + dr;
    const nc = c + dc;
    if (isValid(nr, nc)) {
        const connectionKey = `${r},${c}-${nr},${nc}`;
        const isFromHQ = boardSchema[r][c] === 3; // 检查是否从大本营出发
        if (!isFromHQ && (isCamp(r, c) || explicitDiagonalConnections.has(connectionKey))) {
            addMove({ r: nr, c: nc });
        }
    }
}
```

### 2. 移除大本营的显式对角连接
**位置**: `game.js` 第76-79行和第100-103行

**修复前**:
```javascript
'10,0-11,1', '11,1-10,0', // (11,1) is HQ - 允许双向
'10,2-11,1', '11,1-10,2', // (11,1) is HQ - 允许双向
'10,2-11,3', '11,3-10,2', // (11,3) is HQ - 允许双向
'10,4-11,3', '11,3-10,4', // (11,3) is HQ - 允许双向

'1,0-0,1', '0,1-1,0', // (0,1) is HQ - 允许双向
'1,2-0,1', '0,1-1,2', // (0,1) is HQ - 允许双向
'1,2-0,3', '0,3-1,2', // (0,3) is HQ - 允许双向
'1,4-0,3', '0,3-1,4', // (0,3) is HQ - 允许双向
```

**修复后**:
```javascript
'10,0-11,1', // 只允许到大本营，不允许从大本营出发
'10,2-11,1', // 只允许到大本营，不允许从大本营出发
'10,2-11,3', // 只允许到大本营，不允许从大本营出发
'10,4-11,3', // 只允许到大本营，不允许从大本营出发

'1,0-0,1', // 只允许到大本营，不允许从大本营出发
'1,2-0,1', // 只允许到大本营，不允许从大本营出发
'1,2-0,3', // 只允许到大本营，不允许从大本营出发
'1,4-0,3', // 只允许到大本营，不允许从大本营出发
```

## 棋盘位置说明
- **大本营位置**:
  - AI方: (0,1), (0,3)
  - 玩家方: (11,1), (11,3)
- **棋盘模式值**:
  - `1` = 普通位置
  - `2` = 营地（camp）
  - `3` = 大本营（HQ）

## 修复效果
1. ✅ 大本营中的棋子现在只能进行直线移动（上下左右）
2. ✅ 大本营中的棋子不能进行斜向移动
3. ✅ 其他棋子可以斜向移动到大本营（如果有有效连接）
4. ✅ 营地中的棋子仍然可以进行斜向移动（符合规则）
5. ✅ 保持了AI增强功能的完整性

## 测试建议
1. 开始游戏后，尝试选择大本营中的棋子（如军旗）
2. 验证只显示直线移动选项，没有斜向移动选项
3. 验证营地中的棋子仍然可以斜向移动
4. 验证AI在大本营中的棋子也遵循相同规则

这个修复确保了游戏严格遵循军旗的正确规则，同时保持了之前增强的AI智能。
