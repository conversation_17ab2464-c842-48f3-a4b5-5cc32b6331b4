<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>军旗游戏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Material+Icons" rel="stylesheet"/>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="board-wrapper">
            <div id="game-board" class="board"></div>
        </div>
        <div class="controls w-[320px] flex flex-col gap-6">
            <h1 class="text-2xl font-bold text-gray-800 text-center">游戏控制</h1>

            <!-- 控制面板 -->
            <div class="bg-gray-50 p-6 rounded-lg shadow-sm border border-gray-200">
                <div class="mb-4">
                    <div class="text-sm text-gray-600 mb-4">
                        <p class="font-medium mb-2">🎲 随机布局对战</p>
                        <p>每局游戏将为你和AI随机分配不同的经典布局，增加游戏的变化性和挑战性！</p>
                    </div>
                </div>
                <div class="flex flex-col gap-3">
                    <button id="start-button" class="modern-btn modern-btn-primary">
                        <span class="material-icons mr-2">play_arrow</span>
                        开始随机对战
                    </button>
                    <button id="reset-button" class="modern-btn modern-btn-secondary" disabled>
                        <span class="material-icons mr-2">refresh</span>
                        重新开始
                    </button>
                </div>
            </div>

            <!-- 布局信息 -->
            <div id="layout-info" class="bg-white p-4 rounded-lg shadow-md border border-gray-200">
                <div class="text-sm text-gray-500 text-center">
                    <span class="material-icons mr-2">shuffle</span>
                    开始游戏后将显示布局信息
                </div>
            </div>

            <!-- 游戏状态 -->
            <div id="game-status" class="status-panel status-waiting">
                <div class="flex items-center">
                    <span class="material-icons mr-2">info</span>
                    <div>
                        <p class="font-bold">游戏状态</p>
                        <p>请选择布局并开始游戏。</p>
                    </div>
                </div>
            </div>

            <!-- 被吃掉的棋子 -->
            <div class="bg-white p-6 rounded-lg shadow-md border border-gray-200">
                <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <span class="material-icons mr-2">military_tech</span>
                    被吃掉的棋子
                </h2>
                <div>
                    <h3 class="text-sm font-medium text-gray-600 mb-2">你:</h3>
                    <div id="player-captured" class="flex flex-wrap gap-2 min-h-[2rem]"></div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-600 mb-2">AI:</h3>
                    <div id="ai-captured" class="flex flex-wrap gap-2 min-h-[2rem]"></div>
                </div>
            </div>
        </div>
    </div>
    <script src="game.js"></script>
</body>
</html>
