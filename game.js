document.addEventListener('DOMContentLoaded', () => {
    const boardElement = document.getElementById('game-board');
    const startButton = document.getElementById('start-button');
    const resetButton = document.getElementById('reset-button');
    const layoutSelect = document.getElementById('layout-select');
    const statusElement = document.getElementById('game-status');
    const playerCapturedElement = document.getElementById('player-captured');
    const aiCapturedElement = document.getElementById('ai-captured');

    const ROWS = 12;
    const COLS = 5;
    const PLAYER = 'player';
    const AI = 'ai';

    const PIECE_RANKS = {
        '司令': 10, '军长': 9, '师长': 8, '旅长': 7, '团长': 6, '营长': 5, '连长': 4, '排长': 3, '工兵': 2,
        '炸弹': 11, '地雷': 1, '军旗': 0
    };

    // --- NEW: Coordinate system based on the background image ---
    const boardCoordinates = [
        // Row 0 (AI Top)
        [{ x: 11, y: 7 }, { x: 30, y: 7 }, { x: 50, y: 7 }, { x: 70, y: 7 }, { x: 89, y: 7 }],
        // Row 1
        [{ x: 11, y: 14 }, { x: 30, y: 14 }, { x: 50, y: 14 }, { x: 70, y: 14 }, { x: 89, y: 14 }],
        // Row 2
        [{ x: 11, y: 19.5 }, { x: 30, y: 19.5 }, { x: 50, y: 19.5 }, { x: 70, y: 19.5 }, { x: 89, y: 19.5 }],
        // Row 3
        [{ x: 11, y: 26.5 }, { x: 30, y: 26.5 }, { x: 50, y: 26.5 }, { x: 70, y: 26.5 }, { x: 89, y: 26.5 }],
        // Row 4
        [{ x: 11, y: 33 }, { x: 30, y: 33 }, { x: 50, y: 33 }, { x: 70, y: 33 }, { x: 89, y: 33 }],
        // Row 5 (AI Frontline)
        [{ x: 11, y: 39 }, { x: 30, y: 39 }, { x: 50, y: 39 }, { x: 70, y: 39 }, { x: 89, y: 39 }],
        // --- Middle Line ---
        // Row 6 (Player Frontline)
        [{ x: 11, y: 58.5 }, { x: 30, y: 58.5 }, { x: 50, y: 58.5 }, { x: 70, y: 58.5 }, { x: 89, y: 58.5 }],
        // Row 7
        [{ x: 11, y: 65 }, { x: 30, y: 65 }, { x: 50, y: 65 }, { x: 70, y: 65 }, { x: 89, y: 65 }],
        // Row 8
        [{ x: 11, y: 71 }, { x: 30, y: 71 }, { x: 50, y: 71 }, { x: 70, y: 71 }, { x: 89, y: 71 }],
        // Row 9
        [{ x: 11, y: 78 }, { x: 30, y: 78 }, { x: 50, y: 78 }, { x: 70, y: 78 }, { x: 89, y: 78 }],
        // Row 10
        [{ x: 11, y: 84 }, { x: 30, y: 84 }, { x: 50, y: 84 }, { x: 70, y: 84 }, { x: 89, y: 84 }],
        // Row 11 (Player Bottom)
        [{ x: 11, y: 90 }, { x: 30, y: 90 }, { x: 50, y: 90 }, { x: 70, y: 90 }, { x: 89, y: 90 }],
    ];

    const boardSchema = [
        [1, 3, 1, 3, 1], // Row 0, HQs
        [1, 1, 1, 1, 1], // Row 1, Camp
        [1, 2, 1, 2, 1], // Row 2, Camps
        [1, 1, 2, 1, 1], // Row 3, Camp
        [1, 2, 1, 2, 1], // Row 4
        [1, 1, 1, 1, 1], // Row 5 (middle line)
        [1, 1, 1, 1, 1], // Row 6 (middle line)
        [1, 2, 1, 2, 1], // Row 7
        [1, 1, 2, 1, 1], // Row 8, Camp
        [1, 2, 1, 2, 1], // Row 9, Camps
        [1, 1, 1, 1, 1], // Row 10, Camp
        [1, 3, 1, 3, 1], // Row 11, HQs
    ];

    const explicitDiagonalConnections = new Set([
        // Player side (bottom half) - only connections involving camps
        '8,0-9,1', '9,1-8,0', // (9,1) is camp
        '8,2-9,1', '9,1-8,2', // (8,2) and (9,1) are camps
        '8,2-9,3', '9,3-8,2', // (8,2) and (9,3) are camps
        '8,4-9,3', '9,3-8,4', // (9,3) is camp

        '9,1-10,0', '10,0-9,1', // (9,1) is camp
        '9,1-10,2', '10,2-9,1', // (9,1) is camp
        '9,3-10,2', '10,2-9,3', // (9,3) is camp
        '9,3-10,4', '10,4-9,3', // (9,3) is camp

        '10,0-11,1', '11,1-10,0', // (11,1) is HQ
        '10,2-11,1', '11,1-10,2', // (11,1) is HQ
        '10,2-11,3', '11,3-10,2', // (11,3) is HQ
        '10,4-11,3', '11,3-10,4', // (11,3) is HQ

        // Crucial missing connections (player side) - only involving camps
        '8,0-7,1', '7,1-8,0', // (7,1) is camp
        '8,4-7,3', '7,3-8,4', // (7,3) is camp
        '6,4-7,3', '7,3-6,4', // (7,3) is camp
        '7,1-6,0', '6,0-7,1', // (7,1) is camp
        '7,1-6,2', '6,2-7,1', // (7,1) is camp
        '7,3-6,2', '6,2-7,3', // (7,3) is camp

        // AI side (top half) - only connections involving camps
        '3,0-2,1', '2,1-3,0', // (2,1) is camp
        '3,2-2,1', '2,1-3,2', // (3,2) and (2,1) are camps
        '3,2-2,3', '2,3-3,2', // (3,2) and (2,3) are camps
        '3,4-2,3', '2,3-3,4', // (2,3) is camp

        '2,1-1,0', '1,0-2,1', // (2,1) is camp
        '2,1-1,2', '1,2-2,1', // (2,1) is camp
        '2,3-1,2', '1,2-2,3', // (2,3) is camp
        '2,3-1,4', '1,4-2,3', // (2,3) is camp

        '1,0-0,1', '0,1-1,0', // (0,1) is HQ
        '1,2-0,1', '0,1-1,2', // (0,1) is HQ
        '1,2-0,3', '0,3-1,2', // (0,3) is HQ
        '1,4-0,3', '0,3-1,4', // (0,3) is HQ

        // Crucial missing connections (AI side) - only involving camps
        '3,0-4,1', '4,1-3,0', // (4,1) is camp
        '3,4-4,3', '4,3-3,4', // (4,3) is camp
        '5,0-4,1', '4,1-5,0', // (4,1) is camp
    ]);

    let boardState = [];
    let gameState = {
        currentPlayer: PLAYER,
        selectedPiece: null,
        gameActive: false,
        playerPieces: new Map(),
        aiPieces: new Map(),
    };

    const createPiece = (name, owner) => ({
        name,
        owner,
        rank: PIECE_RANKS[name],
        revealed: true, // DEBUG: All pieces are visible
        id: `${owner}-${name}-${Math.random().toString(36).substr(2, 5)}`
    });

    const layouts = {
        layout1: { // Screenshot-based Defensive Layout
            '司令': [11, 3],
            '军长': [11, 2],
            '师长': [[9, 0], [9, 4]],
            '旅长': [[8, 0], [8, 4]],
            '团长': [[9, 2], [10, 2]],
            '营长': [[8, 1], [8, 3]],
            '连长': [[6, 0], [6, 2], [6, 4]],
            '排长': [[7, 0], [7, 2], [7, 4]],
            '工兵': [[6, 1], [6, 3], [11, 0]],
            '炸弹': [[10, 1], [10, 3]],
            '地雷': [[10, 0], [10, 4], [11, 4]],
            '军旗': [11, 1]
        },
        layout2: { // New Aggressive Layout
            '司令': [11, 3],
            '军长': [9, 0],
            '师长': [[9, 4], [8, 0]],
            '旅长': [[8, 4], [8, 1]],
            '团长': [[8, 3], [9, 2]],
            '营长': [[6, 2], [10, 2]],
            '连长': [[6, 0], [6, 1], [6, 3]],
            '排长': [[6, 4], [7, 0], [7, 2]],
            '工兵': [[7, 4], [10, 1], [11, 0]],
            '炸弹': [[10, 3], [10, 0]],
            '地雷': [[10, 4], [11, 2], [11, 4]],
            '军旗': [11, 1]
        }
    };

    function setupBoard(layout) {
        boardState = Array(ROWS).fill(null).map(() => Array(COLS).fill(null));
        gameState.playerPieces.clear();
        gameState.aiPieces.clear();
        playerCapturedElement.innerHTML = '';
        aiCapturedElement.innerHTML = '';

        const placePiece = (piece, row, col) => {
            boardState[row][col] = piece;
            const pieceMap = piece.owner === PLAYER ? gameState.playerPieces : gameState.aiPieces;
            pieceMap.set(piece.id, { piece, row, col });
        };

        const playerLayout = layouts[layout];
        for (const name in playerLayout) {
            const positions = playerLayout[name];
            if (Array.isArray(positions[0])) {
                positions.forEach(pos => placePiece(createPiece(name, PLAYER), pos[0], pos[1]));
            } else {
                placePiece(createPiece(name, PLAYER), positions[0], positions[1]);
            }
        }

        for (const name in playerLayout) {
            const positions = playerLayout[name];
            if (Array.isArray(positions[0])) {
                positions.forEach(pos => {
                    placePiece(createPiece(name, AI), ROWS - 1 - pos[0], COLS - 1 - pos[1]);
                });
            } else {
                placePiece(createPiece(name, AI), ROWS - 1 - positions[0], COLS - 1 - positions[1]);
            }
        }
    }

    // --- REWRITTEN: renderBoard using absolute positioning ---
    function renderBoard() {
        boardElement.innerHTML = ''; // Clear previous elements

        for (let r = 0; r < ROWS; r++) {
            for (let c = 0; c < COLS; c++) {
                const coords = boardCoordinates[r][c];
                const piece = boardState[r][c];

                if (piece) {
                    // If there is a piece, create one element for both visuals and interaction
                    const pieceElement = document.createElement('div');
                    pieceElement.className = `piece ${piece.owner}`;
                    pieceElement.dataset.row = r;
                    pieceElement.dataset.col = c;
                    pieceElement.dataset.pieceId = piece.id;
                    pieceElement.style.left = `${coords.x}%`;
                    pieceElement.style.top = `${coords.y}%`;
                    pieceElement.textContent = piece.name === '军旗' ? '旗' : piece.name.charAt(0);
                    boardElement.appendChild(pieceElement);
                } else {
                    // If there is no piece, create a transparent cell for movement targets
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = r;
                    cell.dataset.col = c;
                    cell.style.left = `${coords.x}%`;
                    cell.style.top = `${coords.y}%`;
                    boardElement.appendChild(cell);
                }
            }
        }
        addEventListeners();
    }

    function getValidMoves(piece, r, c) {
        if (piece.name === '军旗' || piece.name === '地雷') return [];

        const moves = [];
        const seen = new Set();
        const addMove = (move) => {
            const key = `${move.r},${move.c}`;
            if (seen.has(key)) return;

            const targetPiece = boardState[move.r][move.c];
            if (targetPiece && targetPiece.owner === piece.owner) return; // Cannot attack own piece
            if (boardSchema[move.r][move.c] === 2 && targetPiece) return; // Cannot enter occupied camp
            if (boardSchema[move.r][move.c] === 3 && piece.owner === (move.r < 6 ? AI : PLAYER)) return; // Cannot enter own HQ
            
            moves.push(move);
            seen.add(key);
        };

        const isRailway = (r, c) => {
            // First check if it's a valid position
            if (!isValid(r, c)) return false;

            // Camps and HQs are not railways, even if they're on railway lines
            if (boardSchema[r][c] === 2 || boardSchema[r][c] === 3) return false;

            // Vertical railways are on columns 0 and 4
            const isVerticalRail = (c === 0 || c === 4) && (r >= 1 && r <= 10);
            // Horizontal railways are on rows 1, 5, 6, 10
            const isHorizontalRail = (r === 1 || r === 5 || r === 6 || r === 10) && (c >= 0 && c <= 4);
            return isVerticalRail || isHorizontalRail;
        };
        const isCamp = (r, c) => isValid(r, c) && boardSchema[r][c] === 2;

        // --- Rule 1: Standard Moves (1 step) ---
        const cardinalDirections = [[-1, 0], [1, 0], [0, -1], [0, 1]];
        const diagonalDirections = [[-1, -1], [-1, 1], [1, -1], [1, 1]];

        // All pieces can move 1 step cardinally
        for (const [dr, dc] of cardinalDirections) {
            const nr = r + dr;
            const nc = c + dc;
            if (isValid(nr, nc)) {
                addMove({ r: nr, c: nc });
            }
        }

        // Diagonal moves: based on explicit connections or if starting from a camp
        for (const [dr, dc] of diagonalDirections) {
            const nr = r + dr;
            const nc = c + dc;
            if (isValid(nr, nc)) {
                const connectionKey = `${r},${c}-${nr},${nc}`;
                if (isCamp(r, c) || explicitDiagonalConnections.has(connectionKey)) {
                    addMove({ r: nr, c: nc });
                }
            }
        }

        // --- Rule 2: Railway Moves ---
        if (isRailway(r, c)) {
            if (piece.name === '工兵') {
                // Engineer uses BFS to find all reachable railway squares, and can step off.
                const queue = [[r, c]];
                const visited = new Set([`${r},${c}`]);

                while (queue.length > 0) {
                    const [currentR, currentC] = queue.shift();

                    // Explore adjacent squares (only cardinal directions for railway movement)
                    for (const [dr, dc] of cardinalDirections) {
                        const nextR = currentR + dr;
                        const nextC = currentC + dc;
                        const key = `${nextR},${nextC}`;

                        if (isValid(nextR, nextC) && !visited.has(key)) {
                            if (isRailway(nextR, nextC)) {
                                // Continue on railway
                                const target = boardState[nextR][nextC];
                                if (target === null) {
                                    addMove({ r: nextR, c: nextC });
                                    visited.add(key);
                                    queue.push([nextR, nextC]); // Continue BFS only on railway
                                } else {
                                    addMove({ r: nextR, c: nextC }); // Allow capture, but don't traverse
                                    visited.add(key);
                                }
                            } else if (explicitDiagonalConnections.has(`${currentR},${currentC}-${nextR},${nextC}`)) {
                                // Step off railway via explicit diagonal connection
                                addMove({ r: nextR, c: nextC });
                                visited.add(key);
                                // Don't add to queue - can't continue railway movement from non-railway position
                            }
                        }
                    }
                }
            } else {
                // Other pieces move in straight lines on the railway
                for (const [dr, dc] of cardinalDirections) {
                    for (let i = 1; ; i++) {
                        const nr = r + i * dr;
                        const nc = c + i * dc;
                        if (!isValid(nr, nc) || !isRailway(nr, nc)) break;
                        
                        const target = boardState[nr][nc];
                        if (target) {
                            addMove({ r: nr, c: nc }); // Can move to capture
                            break; // Path is blocked
                        } else {
                            addMove({ r: nr, c: nc });
                        }
                    }
                }
            }
        }

        return moves;
    }

    function isValid(r, c) {
        return r >= 0 && r < ROWS && c >= 0 && c < COLS;
    }

    function handleCellClick(e) {
        if (!gameState.gameActive || gameState.currentPlayer !== PLAYER) return;

        const target = e.target.closest('.piece, .cell');
        if (!target) {
            // Click was outside of any valid cell or piece
            return;
        }

        const { row, col } = target.dataset;
        const r = parseInt(row), c = parseInt(col);
        const piece = boardState[r][c];

        if (gameState.selectedPiece) {
            const { piece: selected, row: fromR, col: fromC } = gameState.selectedPiece;
            const validMoves = getValidMoves(selected, fromR, fromC);

            if (validMoves.some(m => m.r === r && m.c === c)) {
                movePiece(selected, fromR, fromC, r, c);
            } else if (piece && piece.owner === PLAYER) {
                selectPiece(piece, r, c);
            } else {
                deselect();
            }
        } else if (piece && piece.owner === PLAYER) {
            selectPiece(piece, r, c);
        }
    }

    function selectPiece(piece, r, c) {
        deselect();
        gameState.selectedPiece = { piece, row: r, col: c };

        const pieceElement = document.querySelector(`[data-piece-id="${piece.id}"]`);
        if (pieceElement) pieceElement.classList.add('selected');

        const validMoves = getValidMoves(piece, r, c);
        validMoves.forEach(move => {
            const cellElement = document.querySelector(`[data-row="${move.r}"][data-col="${move.c}"]`);
            if (cellElement) cellElement.classList.add('movable-hint');
        });
    }

    function deselect() {
        if (gameState.selectedPiece) {
            const { piece } = gameState.selectedPiece;
            const pieceElement = document.querySelector(`[data-piece-id="${piece.id}"]`);
            if (pieceElement) pieceElement.classList.remove('selected');
        }
        document.querySelectorAll('.movable-hint').forEach(el => el.classList.remove('movable-hint'));
        gameState.selectedPiece = null;
    }

    function movePiece(piece, fromR, fromC, toR, toC) {
        const targetPiece = boardState[toR][toC];
        deselect();

        if (targetPiece) {
            resolveAttack(piece, targetPiece, fromR, fromC, toR, toC);
        } else {
            boardState[toR][toC] = piece;
            boardState[fromR][fromC] = null;
            updatePiecePosition(piece, toR, toC);
        }

        renderBoard();
        checkWinCondition();
        if (gameState.gameActive) {
            switchTurn();
        }
    }

    function resolveAttack(attacker, defender, fromR, fromC, toR, toC) {
        let winner = null;
        let draw = false;

        if (boardSchema[toR][toC] === 2) { // Defender in camp is immune
            return; 
        }
        if (attacker.name === '炸弹' || defender.name === '炸弹') {
            draw = true;
        } else if (defender.name === '地雷') {
            winner = attacker.name === '工兵' ? attacker : null;
        } else if (defender.name === '军旗') {
            winner = attacker;
        } else if (attacker.rank >= defender.rank) {
            winner = attacker;
            if (attacker.rank === defender.rank) draw = true;
        } else {
            winner = defender;
        }

        if (draw) {
            capturePiece(attacker);
            capturePiece(defender);
            boardState[fromR][fromC] = null;
            boardState[toR][toC] = null;
            removePieceFromGame(attacker);
            removePieceFromGame(defender);
        } else if (winner === attacker) {
            capturePiece(defender);
            boardState[toR][toC] = attacker;
            boardState[fromR][fromC] = null;
            updatePiecePosition(attacker, toR, toC);
            removePieceFromGame(defender);
        } else { // defender wins
            capturePiece(attacker);
            boardState[fromR][fromC] = null;
            removePieceFromGame(attacker);
        }
    }

    function capturePiece(piece) {
        const capturedElement = piece.owner === PLAYER ? aiCapturedElement : playerCapturedElement;
        const pieceSpan = document.createElement('span');
        pieceSpan.className = 'captured-piece';
        pieceSpan.textContent = piece.name === '军旗' ? '旗' : piece.name.charAt(0);
        capturedElement.appendChild(pieceSpan);
    }

    function removePieceFromGame(piece) {
        const pieceMap = piece.owner === PLAYER ? gameState.playerPieces : gameState.aiPieces;
        pieceMap.delete(piece.id);
    }

    function updatePiecePosition(piece, newR, newC) {
        const pieceMap = piece.owner === PLAYER ? gameState.playerPieces : gameState.aiPieces;
        if (pieceMap.has(piece.id)) {
            pieceMap.set(piece.id, { piece, row: newR, col: newC });
        }
    }

    function switchTurn() {
        gameState.currentPlayer = (gameState.currentPlayer === PLAYER) ? AI : PLAYER;
        updateStatus();
        if (gameState.currentPlayer === AI) {
            setTimeout(executeAITurn, 500);
        }
    }

    function updateStatus(message, type = 'waiting') {
        // 移除所有状态类
        statusElement.className = statusElement.className.replace(/status-\w+/g, '');
        statusElement.className += ' status-panel';

        if (message) {
            // 根据消息类型添加相应的状态类和图标
            let icon = 'info';
            let title = '游戏状态';

            if (message.includes('你赢了')) {
                statusElement.className += ' status-win';
                icon = 'emoji_events';
                title = '恭喜!';
            } else if (message.includes('AI赢了')) {
                statusElement.className += ' status-lose';
                icon = 'sentiment_dissatisfied';
                title = '游戏结束';
            } else {
                statusElement.className += ` status-${type}`;
                if (type === 'playing') {
                    icon = 'sports_esports';
                    title = '游戏进行中';
                }
            }

            statusElement.innerHTML = `
                <div class="flex items-center">
                    <span class="material-icons mr-2">${icon}</span>
                    <div>
                        <p class="font-bold">${title}</p>
                        <p>${message}</p>
                    </div>
                </div>
            `;
            return;
        }

        const turnText = gameState.currentPlayer === PLAYER ? "你的回合" : "AI回合";
        statusElement.className += ' status-playing';
        statusElement.innerHTML = `
            <div class="flex items-center">
                <span class="material-icons mr-2">sports_esports</span>
                <div>
                    <p class="font-bold">游戏进行中</p>
                    <p>${turnText}</p>
                </div>
            </div>
        `;
    }

    function checkWinCondition() {
        const playerFlag = [...gameState.playerPieces.values()].find(p => p.piece.name === '军旗');
        const aiFlag = [...gameState.aiPieces.values()].find(p => p.piece.name === '军旗');

        if (!aiFlag) {
            endGame("你赢了！你扛了对方的军旗！");
            return true;
        }
        if (!playerFlag) {
            endGame("AI赢了！你的军旗被扛了！");
            return true;
        }

        const playerHasMoves = [...gameState.playerPieces.values()].some(p => getValidMoves(p.piece, p.row, p.col).length > 0);
        if (!playerHasMoves) {
            endGame("AI赢了！你无子可动！");
            return true;
        }
        
        const aiHasMoves = [...gameState.aiPieces.values()].some(p => getValidMoves(p.piece, p.row, p.col).length > 0);
        if (!aiHasMoves) {
            endGame("你赢了！AI无子可动！");
            return true;
        }
        return false;
    }
    
    function endGame(message) {
        gameState.gameActive = false;
        updateStatus(message);
        resetButton.disabled = false;
        startButton.disabled = true;
    }

    function startGame() {
        const selectedLayout = layoutSelect.value;
        setupBoard(selectedLayout);
        renderBoard();
        gameState.gameActive = true;
        gameState.currentPlayer = PLAYER;
        startButton.disabled = true;
        resetButton.disabled = false;
        layoutSelect.disabled = true;
        updateStatus();
    }

    function resetGame() {
        gameState.gameActive = false;
        boardElement.innerHTML = '';
        playerCapturedElement.innerHTML = '';
        aiCapturedElement.innerHTML = '';
        startButton.disabled = false;
        resetButton.disabled = true;
        layoutSelect.disabled = false;
        updateStatus("请选择布局并开始游戏。", 'waiting');
    }

    function addEventListeners() {
        document.querySelectorAll('.piece, .cell').forEach(el => {
            el.addEventListener('click', handleCellClick);
        });
    }

    function executeAITurn() {
        if (!gameState.gameActive) return;
        const bestMove = findBestMove();
        if (bestMove) {
            const { piece, fromR, fromC, toR, toC } = bestMove;
            movePiece(piece, fromR, fromC, toR, toC);
        } else {
            checkWinCondition();
        }
    }

    function findBestMove() {
        let bestMove = null;
        let maxScore = -Infinity;

        const aiPieces = [...gameState.aiPieces.values()];
        aiPieces.sort(() => Math.random() - 0.5);

        for (const { piece, row, col } of aiPieces) {
            const validMoves = getValidMoves(piece, row, col);
            for (const move of validMoves) {
                const score = evaluateMove(piece, row, col, move.r, move.c);
                if (score > maxScore) {
                    maxScore = score;
                    bestMove = { piece, fromR: row, fromC: col, toR: move.r, toC: move.c };
                }
            }
        }
        return bestMove;
    }

    function evaluateMove(piece, fromR, fromC, toR, toC) {
        let score = 0;
        const targetPiece = boardState[toR][toC];

        if (targetPiece) {
            if (targetPiece.name === '军旗') return 1000;

            if (piece.name === '炸弹') {
                score += (targetPiece.rank * 2);
            } else if (targetPiece.name === '炸弹') {
                score -= 100;
            } else if (targetPiece.name === '地雷' && piece.name !== '工兵') {
                score -= 100;
            } else if (piece.rank >= targetPiece.rank) {
                score += 10 + (targetPiece.rank * 2);
            } else {
                score -= 10 + (piece.rank * 2);
            }
        } else {
            score += 1;
            if (toR > fromR) {
                score += 0.5;
            }
        }
        
        score += Math.random() * 0.1;
        return score;
    }

    startButton.addEventListener('click', startGame);
    resetButton.addEventListener('click', resetGame);
});