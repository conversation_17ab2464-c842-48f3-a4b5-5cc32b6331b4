document.addEventListener('DOMContentLoaded', () => {
    const boardElement = document.getElementById('game-board');
    const startButton = document.getElementById('start-button');
    const resetButton = document.getElementById('reset-button');
    // layoutSelect removed - now using random layouts
    const statusElement = document.getElementById('game-status');
    const playerCapturedElement = document.getElementById('player-captured');
    const aiCapturedElement = document.getElementById('ai-captured');

    const ROWS = 12;
    const COLS = 5;
    const PLAYER = 'player';
    const AI = 'ai';

    const PIECE_RANKS = {
        '司令': 10, '军长': 9, '师长': 8, '旅长': 7, '团长': 6, '营长': 5, '连长': 4, '排长': 3, '工兵': 2,
        '炸弹': 11, '地雷': 1, '军旗': 0
    };

    // --- NEW: Coordinate system based on the background image ---
    const boardCoordinates = [
        // Row 0 (AI Top)
        [{ x: 11, y: 7 }, { x: 30, y: 7 }, { x: 50, y: 7 }, { x: 70, y: 7 }, { x: 89, y: 7 }],
        // Row 1
        [{ x: 11, y: 14 }, { x: 30, y: 14 }, { x: 50, y: 14 }, { x: 70, y: 14 }, { x: 89, y: 14 }],
        // Row 2
        [{ x: 11, y: 19.5 }, { x: 30, y: 19.5 }, { x: 50, y: 19.5 }, { x: 70, y: 19.5 }, { x: 89, y: 19.5 }],
        // Row 3
        [{ x: 11, y: 26.5 }, { x: 30, y: 26.5 }, { x: 50, y: 26.5 }, { x: 70, y: 26.5 }, { x: 89, y: 26.5 }],
        // Row 4
        [{ x: 11, y: 33 }, { x: 30, y: 33 }, { x: 50, y: 33 }, { x: 70, y: 33 }, { x: 89, y: 33 }],
        // Row 5 (AI Frontline)
        [{ x: 11, y: 39 }, { x: 30, y: 39 }, { x: 50, y: 39 }, { x: 70, y: 39 }, { x: 89, y: 39 }],
        // --- Middle Line ---
        // Row 6 (Player Frontline)
        [{ x: 11, y: 58.5 }, { x: 30, y: 58.5 }, { x: 50, y: 58.5 }, { x: 70, y: 58.5 }, { x: 89, y: 58.5 }],
        // Row 7
        [{ x: 11, y: 65 }, { x: 30, y: 65 }, { x: 50, y: 65 }, { x: 70, y: 65 }, { x: 89, y: 65 }],
        // Row 8
        [{ x: 11, y: 71 }, { x: 30, y: 71 }, { x: 50, y: 71 }, { x: 70, y: 71 }, { x: 89, y: 71 }],
        // Row 9
        [{ x: 11, y: 78 }, { x: 30, y: 78 }, { x: 50, y: 78 }, { x: 70, y: 78 }, { x: 89, y: 78 }],
        // Row 10
        [{ x: 11, y: 84 }, { x: 30, y: 84 }, { x: 50, y: 84 }, { x: 70, y: 84 }, { x: 89, y: 84 }],
        // Row 11 (Player Bottom)
        [{ x: 11, y: 90 }, { x: 30, y: 90 }, { x: 50, y: 90 }, { x: 70, y: 90 }, { x: 89, y: 90 }],
    ];

    const boardSchema = [
        [1, 3, 1, 3, 1], // Row 0, HQs
        [1, 1, 1, 1, 1], // Row 1, Camp
        [1, 2, 1, 2, 1], // Row 2, Camps
        [1, 1, 2, 1, 1], // Row 3, Camp
        [1, 2, 1, 2, 1], // Row 4
        [1, 1, 1, 1, 1], // Row 5 (middle line)
        [1, 1, 1, 1, 1], // Row 6 (middle line)
        [1, 2, 1, 2, 1], // Row 7
        [1, 1, 2, 1, 1], // Row 8, Camp
        [1, 2, 1, 2, 1], // Row 9, Camps
        [1, 1, 1, 1, 1], // Row 10, Camp
        [1, 3, 1, 3, 1], // Row 11, HQs
    ];

    const explicitDiagonalConnections = new Set([
        // Player side (bottom half) - only connections involving camps
        '8,0-9,1', '9,1-8,0', // (9,1) is camp
        '8,2-9,1', '9,1-8,2', // (8,2) and (9,1) are camps
        '8,2-9,3', '9,3-8,2', // (8,2) and (9,3) are camps
        '8,4-9,3', '9,3-8,4', // (9,3) is camp

        '9,1-10,0', '10,0-9,1', // (9,1) is camp
        '9,1-10,2', '10,2-9,1', // (9,1) is camp
        '9,3-10,2', '10,2-9,3', // (9,3) is camp
        '9,3-10,4', '10,4-9,3', // (9,3) is camp

        '10,0-11,1', // 只允许到大本营，不允许从大本营出发
        '10,2-11,1', // 只允许到大本营，不允许从大本营出发
        '10,2-11,3', // 只允许到大本营，不允许从大本营出发
        '10,4-11,3', // 只允许到大本营，不允许从大本营出发

        // Crucial missing connections (player side) - only involving camps
        '8,0-7,1', '7,1-8,0', // (7,1) is camp
        '8,4-7,3', '7,3-8,4', // (7,3) is camp
        '6,4-7,3', '7,3-6,4', // (7,3) is camp
        '7,1-6,0', '6,0-7,1', // (7,1) is camp
        '7,1-6,2', '6,2-7,1', // (7,1) is camp
        '7,3-6,2', '6,2-7,3', // (7,3) is camp

        // AI side (top half) - only connections involving camps
        '3,0-2,1', '2,1-3,0', // (2,1) is camp
        '3,2-2,1', '2,1-3,2', // (3,2) and (2,1) are camps
        '3,2-2,3', '2,3-3,2', // (3,2) and (2,3) are camps
        '3,4-2,3', '2,3-3,4', // (2,3) is camp

        '2,1-1,0', '1,0-2,1', // (2,1) is camp
        '2,1-1,2', '1,2-2,1', // (2,1) is camp
        '2,3-1,2', '1,2-2,3', // (2,3) is camp
        '2,3-1,4', '1,4-2,3', // (2,3) is camp

        '1,0-0,1', // 只允许到大本营，不允许从大本营出发
        '1,2-0,1', // 只允许到大本营，不允许从大本营出发
        '1,2-0,3', // 只允许到大本营，不允许从大本营出发
        '1,4-0,3', // 只允许到大本营，不允许从大本营出发

        // Crucial missing connections (AI side) - only involving camps
        '3,0-4,1', '4,1-3,0', // (4,1) is camp
        '3,4-4,3', '4,3-3,4', // (4,3) is camp
        '5,0-4,1', '4,1-5,0', // (4,1) is camp
    ]);

    let boardState = [];
    let gameState = {
        currentPlayer: PLAYER,
        selectedPiece: null,
        gameActive: false,
        playerPieces: new Map(),
        aiPieces: new Map(),
    };

    const createPiece = (name, owner) => ({
        name,
        owner,
        rank: PIECE_RANKS[name],
        revealed: true, // DEBUG: All pieces are visible
        id: `${owner}-${name}-${Math.random().toString(36).substr(2, 5)}`
    });

    const layouts = {
        layout1: { // 经典防守阵型
            name: '经典防守阵',
            '司令': [11, 3],
            '军长': [11, 2],
            '师长': [[9, 0], [9, 4]],
            '旅长': [[8, 0], [8, 4]],
            '团长': [[9, 2], [10, 2]],
            '营长': [[8, 1], [8, 3]],
            '连长': [[6, 0], [6, 2], [6, 4]],
            '排长': [[7, 0], [7, 2], [7, 4]],
            '工兵': [[6, 1], [6, 3], [11, 0]],
            '炸弹': [[10, 1], [10, 3]],
            '地雷': [[10, 0], [10, 4], [11, 4]],
            '军旗': [11, 1]
        },
        layout2: { // 积极进攻阵型
            name: '积极进攻阵',
            '司令': [11, 3],
            '军长': [9, 0],
            '师长': [[9, 4], [8, 0]],
            '旅长': [[8, 4], [8, 1]],
            '团长': [[8, 3], [9, 2]],
            '营长': [[6, 2], [10, 2]],
            '连长': [[6, 0], [6, 1], [6, 3]],
            '排长': [[6, 4], [7, 0], [7, 2]],
            '工兵': [[7, 4], [10, 1], [11, 0]],
            '炸弹': [[10, 3], [10, 0]],
            '地雷': [[10, 4], [11, 2], [11, 4]],
            '军旗': [11, 1]
        },
        layout3: { // 平衡发展阵型
            name: '平衡发展阵',
            '司令': [10, 2],
            '军长': [11, 1],
            '师长': [[8, 1], [8, 3]],
            '旅长': [[9, 0], [9, 4]],
            '团长': [[7, 1], [7, 3]],
            '营长': [[6, 1], [6, 3]],
            '连长': [[6, 0], [6, 2], [6, 4]],
            '排长': [[7, 0], [7, 2], [7, 4]],
            '工兵': [[9, 1], [9, 3], [11, 0]],
            '炸弹': [[10, 1], [10, 3]],
            '地雷': [[10, 0], [10, 4], [11, 4]],
            '军旗': [11, 3]
        },
        layout4: { // 工兵突击阵型
            name: '工兵突击阵',
            '司令': [11, 1],
            '军长': [10, 2],
            '师长': [[9, 1], [9, 3]],
            '旅长': [[8, 0], [8, 4]],
            '团长': [[8, 2], [10, 1]],
            '营长': [[7, 1], [7, 3]],
            '连长': [[6, 1], [6, 3], [9, 0]],
            '排长': [[6, 0], [6, 4], [8, 1]],
            '工兵': [[6, 2], [7, 0], [7, 4]], // 工兵前置
            '炸弹': [[10, 0], [10, 4]],
            '地雷': [[9, 4], [11, 2], [11, 4]],
            '军旗': [11, 3]
        },
        layout5: { // 铁桶防守阵型
            name: '铁桶防守阵',
            '司令': [11, 1],
            '军长': [11, 3],
            '师长': [[10, 1], [10, 3]],
            '旅长': [[9, 0], [9, 4]],
            '团长': [[9, 1], [9, 3]],
            '营长': [[8, 0], [8, 4]],
            '连长': [[7, 0], [7, 2], [7, 4]],
            '排长': [[6, 0], [6, 2], [6, 4]],
            '工兵': [[6, 1], [6, 3], [11, 0]],
            '炸弹': [[8, 1], [8, 3]], // 炸弹前置保护
            '地雷': [[10, 0], [10, 4], [11, 4]],
            '军旗': [11, 2]
        }
    };

    function setupBoard(playerLayoutKey = null, aiLayoutKey = null) {
        boardState = Array(ROWS).fill(null).map(() => Array(COLS).fill(null));
        gameState.playerPieces.clear();
        gameState.aiPieces.clear();
        playerCapturedElement.innerHTML = '';
        aiCapturedElement.innerHTML = '';

        // 随机选择布局，确保玩家和AI使用不同的布局
        const layoutKeys = Object.keys(layouts);

        if (!playerLayoutKey) {
            playerLayoutKey = layoutKeys[Math.floor(Math.random() * layoutKeys.length)];
        }

        if (!aiLayoutKey) {
            // AI选择与玩家不同的布局
            const availableAILayouts = layoutKeys.filter(key => key !== playerLayoutKey);
            aiLayoutKey = availableAILayouts[Math.floor(Math.random() * availableAILayouts.length)];
        }

        // 存储选择的布局信息
        gameState.playerLayout = playerLayoutKey;
        gameState.aiLayout = aiLayoutKey;

        const placePiece = (piece, row, col) => {
            boardState[row][col] = piece;
            const pieceMap = piece.owner === PLAYER ? gameState.playerPieces : gameState.aiPieces;
            pieceMap.set(piece.id, { piece, row, col });
        };

        // 设置玩家棋子
        const playerLayout = layouts[playerLayoutKey];
        for (const name in playerLayout) {
            if (name === 'name') continue; // 跳过布局名称
            const positions = playerLayout[name];
            if (Array.isArray(positions[0])) {
                positions.forEach(pos => placePiece(createPiece(name, PLAYER), pos[0], pos[1]));
            } else {
                placePiece(createPiece(name, PLAYER), positions[0], positions[1]);
            }
        }

        // 设置AI棋子（使用不同的布局）
        const aiLayout = layouts[aiLayoutKey];
        for (const name in aiLayout) {
            if (name === 'name') continue; // 跳过布局名称
            const positions = aiLayout[name];
            if (Array.isArray(positions[0])) {
                positions.forEach(pos => {
                    placePiece(createPiece(name, AI), ROWS - 1 - pos[0], COLS - 1 - pos[1]);
                });
            } else {
                placePiece(createPiece(name, AI), ROWS - 1 - positions[0], COLS - 1 - positions[1]);
            }
        }

        // 显示布局信息
        updateLayoutInfo(layouts[playerLayoutKey].name, layouts[aiLayoutKey].name);
    }

    // 更新布局信息显示
    function updateLayoutInfo(playerLayoutName, aiLayoutName) {
        const layoutInfoElement = document.getElementById('layout-info');
        if (layoutInfoElement) {
            layoutInfoElement.innerHTML = `
                <div class="text-sm text-gray-600">
                    <div class="flex justify-between items-center mb-2">
                        <span class="font-medium">布局信息</span>
                        <span class="material-icons text-lg">info</span>
                    </div>
                    <div class="space-y-1">
                        <div class="flex justify-between">
                            <span>你的布局:</span>
                            <span class="font-medium text-blue-600">${playerLayoutName}</span>
                        </div>
                        <div class="flex justify-between">
                            <span>AI布局:</span>
                            <span class="font-medium text-red-600">${aiLayoutName}</span>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // --- REWRITTEN: renderBoard using absolute positioning ---
    function renderBoard() {
        boardElement.innerHTML = ''; // Clear previous elements

        for (let r = 0; r < ROWS; r++) {
            for (let c = 0; c < COLS; c++) {
                const coords = boardCoordinates[r][c];
                const piece = boardState[r][c];

                if (piece) {
                    // If there is a piece, create one element for both visuals and interaction
                    const pieceElement = document.createElement('div');
                    pieceElement.className = `piece ${piece.owner}`;
                    pieceElement.dataset.row = r;
                    pieceElement.dataset.col = c;
                    pieceElement.dataset.pieceId = piece.id;
                    pieceElement.style.left = `${coords.x}%`;
                    pieceElement.style.top = `${coords.y}%`;
                    pieceElement.textContent = piece.name === '军旗' ? '旗' : piece.name.charAt(0);
                    boardElement.appendChild(pieceElement);
                } else {
                    // If there is no piece, create a transparent cell for movement targets
                    const cell = document.createElement('div');
                    cell.className = 'cell';
                    cell.dataset.row = r;
                    cell.dataset.col = c;
                    cell.style.left = `${coords.x}%`;
                    cell.style.top = `${coords.y}%`;
                    boardElement.appendChild(cell);
                }
            }
        }
        addEventListeners();
    }

    function getValidMoves(piece, r, c) {
        if (piece.name === '军旗' || piece.name === '地雷') return [];

        const moves = [];
        const seen = new Set();
        const addMove = (move) => {
            const key = `${move.r},${move.c}`;
            if (seen.has(key)) return;

            const targetPiece = boardState[move.r][move.c];
            if (targetPiece && targetPiece.owner === piece.owner) return; // Cannot attack own piece
            if (boardSchema[move.r][move.c] === 2 && targetPiece) return; // Cannot enter occupied camp
            if (boardSchema[move.r][move.c] === 3 && piece.owner === (move.r < 6 ? AI : PLAYER)) return; // Cannot enter own HQ
            
            moves.push(move);
            seen.add(key);
        };

        const isRailway = (r, c) => {
            // First check if it's a valid position
            if (!isValid(r, c)) return false;

            // Camps and HQs are not railways, even if they're on railway lines
            if (boardSchema[r][c] === 2 || boardSchema[r][c] === 3) return false;

            // Vertical railways are on columns 0 and 4
            const isVerticalRail = (c === 0 || c === 4) && (r >= 1 && r <= 10);
            // Horizontal railways are on rows 1, 5, 6, 10
            const isHorizontalRail = (r === 1 || r === 5 || r === 6 || r === 10) && (c >= 0 && c <= 4);
            return isVerticalRail || isHorizontalRail;
        };
        const isCamp = (r, c) => isValid(r, c) && boardSchema[r][c] === 2;

        // --- Rule 1: Standard Moves (1 step) ---
        const cardinalDirections = [[-1, 0], [1, 0], [0, -1], [0, 1]];
        const diagonalDirections = [[-1, -1], [-1, 1], [1, -1], [1, 1]];

        // All pieces can move 1 step cardinally
        for (const [dr, dc] of cardinalDirections) {
            const nr = r + dr;
            const nc = c + dc;
            if (isValid(nr, nc)) {
                addMove({ r: nr, c: nc });
            }
        }

        // Diagonal moves: based on explicit connections or if starting from a camp (but NOT from HQ)
        for (const [dr, dc] of diagonalDirections) {
            const nr = r + dr;
            const nc = c + dc;
            if (isValid(nr, nc)) {
                const connectionKey = `${r},${c}-${nr},${nc}`;
                const isFromHQ = boardSchema[r][c] === 3; // 检查是否从大本营出发
                if (!isFromHQ && (isCamp(r, c) || explicitDiagonalConnections.has(connectionKey))) {
                    addMove({ r: nr, c: nc });
                }
            }
        }

        // --- Rule 2: Railway Moves ---
        if (isRailway(r, c)) {
            if (piece.name === '工兵') {
                // Engineer uses BFS to find all reachable railway squares, and can step off.
                const queue = [[r, c]];
                const visited = new Set([`${r},${c}`]);

                while (queue.length > 0) {
                    const [currentR, currentC] = queue.shift();

                    // Explore adjacent squares (only cardinal directions for railway movement)
                    for (const [dr, dc] of cardinalDirections) {
                        const nextR = currentR + dr;
                        const nextC = currentC + dc;
                        const key = `${nextR},${nextC}`;

                        if (isValid(nextR, nextC) && !visited.has(key)) {
                            if (isRailway(nextR, nextC)) {
                                // Continue on railway
                                const target = boardState[nextR][nextC];
                                if (target === null) {
                                    addMove({ r: nextR, c: nextC });
                                    visited.add(key);
                                    queue.push([nextR, nextC]); // Continue BFS only on railway
                                } else {
                                    addMove({ r: nextR, c: nextC }); // Allow capture, but don't traverse
                                    visited.add(key);
                                }
                            } else if (explicitDiagonalConnections.has(`${currentR},${currentC}-${nextR},${nextC}`)) {
                                // Step off railway via explicit diagonal connection
                                addMove({ r: nextR, c: nextC });
                                visited.add(key);
                                // Don't add to queue - can't continue railway movement from non-railway position
                            }
                        }
                    }
                }
            } else {
                // Other pieces move in straight lines on the railway
                for (const [dr, dc] of cardinalDirections) {
                    for (let i = 1; ; i++) {
                        const nr = r + i * dr;
                        const nc = c + i * dc;
                        if (!isValid(nr, nc) || !isRailway(nr, nc)) break;
                        
                        const target = boardState[nr][nc];
                        if (target) {
                            addMove({ r: nr, c: nc }); // Can move to capture
                            break; // Path is blocked
                        } else {
                            addMove({ r: nr, c: nc });
                        }
                    }
                }
            }
        }

        return moves;
    }

    function isValid(r, c) {
        return r >= 0 && r < ROWS && c >= 0 && c < COLS;
    }

    function handleCellClick(e) {
        if (!gameState.gameActive || gameState.currentPlayer !== PLAYER) return;

        const target = e.target.closest('.piece, .cell');
        if (!target) {
            // Click was outside of any valid cell or piece
            return;
        }

        const { row, col } = target.dataset;
        const r = parseInt(row), c = parseInt(col);
        const piece = boardState[r][c];

        if (gameState.selectedPiece) {
            const { piece: selected, row: fromR, col: fromC } = gameState.selectedPiece;
            const validMoves = getValidMoves(selected, fromR, fromC);

            if (validMoves.some(m => m.r === r && m.c === c)) {
                movePiece(selected, fromR, fromC, r, c);
            } else if (piece && piece.owner === PLAYER) {
                selectPiece(piece, r, c);
            } else {
                deselect();
            }
        } else if (piece && piece.owner === PLAYER) {
            selectPiece(piece, r, c);
        }
    }

    function selectPiece(piece, r, c) {
        deselect();
        gameState.selectedPiece = { piece, row: r, col: c };

        const pieceElement = document.querySelector(`[data-piece-id="${piece.id}"]`);
        if (pieceElement) pieceElement.classList.add('selected');

        const validMoves = getValidMoves(piece, r, c);
        validMoves.forEach(move => {
            const cellElement = document.querySelector(`[data-row="${move.r}"][data-col="${move.c}"]`);
            if (cellElement) cellElement.classList.add('movable-hint');
        });
    }

    function deselect() {
        if (gameState.selectedPiece) {
            const { piece } = gameState.selectedPiece;
            const pieceElement = document.querySelector(`[data-piece-id="${piece.id}"]`);
            if (pieceElement) pieceElement.classList.remove('selected');
        }
        document.querySelectorAll('.movable-hint').forEach(el => el.classList.remove('movable-hint'));
        gameState.selectedPiece = null;
    }

    function movePiece(piece, fromR, fromC, toR, toC) {
        const targetPiece = boardState[toR][toC];
        deselect();

        if (targetPiece) {
            resolveAttack(piece, targetPiece, fromR, fromC, toR, toC);
        } else {
            boardState[toR][toC] = piece;
            boardState[fromR][fromC] = null;
            updatePiecePosition(piece, toR, toC);
        }

        renderBoard();
        checkWinCondition();
        if (gameState.gameActive) {
            switchTurn();
        }
    }

    function resolveAttack(attacker, defender, fromR, fromC, toR, toC) {
        let winner = null;
        let draw = false;

        if (boardSchema[toR][toC] === 2) { // Defender in camp is immune
            return; 
        }
        if (attacker.name === '炸弹' || defender.name === '炸弹') {
            draw = true;
        } else if (defender.name === '地雷') {
            winner = attacker.name === '工兵' ? attacker : null;
        } else if (defender.name === '军旗') {
            winner = attacker;
        } else if (attacker.rank >= defender.rank) {
            winner = attacker;
            if (attacker.rank === defender.rank) draw = true;
        } else {
            winner = defender;
        }

        if (draw) {
            capturePiece(attacker);
            capturePiece(defender);
            boardState[fromR][fromC] = null;
            boardState[toR][toC] = null;
            removePieceFromGame(attacker);
            removePieceFromGame(defender);
        } else if (winner === attacker) {
            capturePiece(defender);
            boardState[toR][toC] = attacker;
            boardState[fromR][fromC] = null;
            updatePiecePosition(attacker, toR, toC);
            removePieceFromGame(defender);
        } else { // defender wins
            capturePiece(attacker);
            boardState[fromR][fromC] = null;
            removePieceFromGame(attacker);
        }
    }

    function capturePiece(piece) {
        const capturedElement = piece.owner === PLAYER ? aiCapturedElement : playerCapturedElement;
        const pieceSpan = document.createElement('span');
        pieceSpan.className = 'captured-piece';
        pieceSpan.textContent = piece.name === '军旗' ? '旗' : piece.name.charAt(0);
        capturedElement.appendChild(pieceSpan);
    }

    function removePieceFromGame(piece) {
        const pieceMap = piece.owner === PLAYER ? gameState.playerPieces : gameState.aiPieces;
        pieceMap.delete(piece.id);
    }

    function updatePiecePosition(piece, newR, newC) {
        const pieceMap = piece.owner === PLAYER ? gameState.playerPieces : gameState.aiPieces;
        if (pieceMap.has(piece.id)) {
            pieceMap.set(piece.id, { piece, row: newR, col: newC });
        }
    }

    function switchTurn() {
        gameState.currentPlayer = (gameState.currentPlayer === PLAYER) ? AI : PLAYER;
        updateStatus();
        if (gameState.currentPlayer === AI) {
            setTimeout(executeAITurn, 500);
        }
    }

    function updateStatus(message, type = 'waiting') {
        // 移除所有状态类
        statusElement.className = statusElement.className.replace(/status-\w+/g, '');
        statusElement.className += ' status-panel';

        if (message) {
            // 根据消息类型添加相应的状态类和图标
            let icon = 'info';
            let title = '游戏状态';

            if (message.includes('你赢了')) {
                statusElement.className += ' status-win';
                icon = 'emoji_events';
                title = '恭喜!';
            } else if (message.includes('AI赢了')) {
                statusElement.className += ' status-lose';
                icon = 'sentiment_dissatisfied';
                title = '游戏结束';
            } else {
                statusElement.className += ` status-${type}`;
                if (type === 'playing') {
                    icon = 'sports_esports';
                    title = '游戏进行中';
                }
            }

            statusElement.innerHTML = `
                <div class="flex items-center">
                    <span class="material-icons mr-2">${icon}</span>
                    <div>
                        <p class="font-bold">${title}</p>
                        <p>${message}</p>
                    </div>
                </div>
            `;
            return;
        }

        const turnText = gameState.currentPlayer === PLAYER ? "你的回合" : "AI回合";
        statusElement.className += ' status-playing';
        statusElement.innerHTML = `
            <div class="flex items-center">
                <span class="material-icons mr-2">sports_esports</span>
                <div>
                    <p class="font-bold">游戏进行中</p>
                    <p>${turnText}</p>
                </div>
            </div>
        `;
    }

    function checkWinCondition() {
        const playerFlag = [...gameState.playerPieces.values()].find(p => p.piece.name === '军旗');
        const aiFlag = [...gameState.aiPieces.values()].find(p => p.piece.name === '军旗');

        if (!aiFlag) {
            endGame("你赢了！你扛了对方的军旗！");
            return true;
        }
        if (!playerFlag) {
            endGame("AI赢了！你的军旗被扛了！");
            return true;
        }

        const playerHasMoves = [...gameState.playerPieces.values()].some(p => getValidMoves(p.piece, p.row, p.col).length > 0);
        if (!playerHasMoves) {
            endGame("AI赢了！你无子可动！");
            return true;
        }
        
        const aiHasMoves = [...gameState.aiPieces.values()].some(p => getValidMoves(p.piece, p.row, p.col).length > 0);
        if (!aiHasMoves) {
            endGame("你赢了！AI无子可动！");
            return true;
        }
        return false;
    }
    
    function endGame(message) {
        gameState.gameActive = false;
        updateStatus(message);
        resetButton.disabled = false;
        startButton.disabled = true;
    }

    function startGame() {
        // 随机选择不同的布局给玩家和AI
        setupBoard(); // 不传参数，让函数内部随机选择
        renderBoard();
        gameState.gameActive = true;
        gameState.currentPlayer = PLAYER;
        startButton.disabled = true;
        resetButton.disabled = false;
        layoutSelect.disabled = true;
        updateStatus();
    }

    function resetGame() {
        gameState.gameActive = false;
        boardElement.innerHTML = '';
        playerCapturedElement.innerHTML = '';
        aiCapturedElement.innerHTML = '';
        startButton.disabled = false;
        resetButton.disabled = true;

        // 重置布局信息显示
        const layoutInfoElement = document.getElementById('layout-info');
        if (layoutInfoElement) {
            layoutInfoElement.innerHTML = `
                <div class="text-sm text-gray-500 text-center">
                    <span class="material-icons mr-2">shuffle</span>
                    开始游戏后将显示布局信息
                </div>
            `;
        }

        updateStatus("点击开始随机对战！", 'waiting');
    }

    function addEventListeners() {
        document.querySelectorAll('.piece, .cell').forEach(el => {
            el.addEventListener('click', handleCellClick);
        });
    }

    function executeAITurn() {
        if (!gameState.gameActive) return;
        const bestMove = findBestMoveAdvanced();
        if (bestMove) {
            const { piece, fromR, fromC, toR, toC } = bestMove;
            movePiece(piece, fromR, fromC, toR, toC);
        } else {
            checkWinCondition();
        }
    }

    // 增强版AI决策系统
    function findBestMoveAdvanced() {
        const depth = 3; // 搜索深度
        const result = minimax(depth, true, -Infinity, Infinity);
        return result.move;
    }

    // Minimax算法与Alpha-Beta剪枝
    function minimax(depth, isMaximizing, alpha, beta) {
        if (depth === 0 || checkWinCondition()) {
            return { score: evaluateBoardState(), move: null };
        }

        const currentPlayer = isMaximizing ? AI : PLAYER;
        const pieces = isMaximizing ? [...gameState.aiPieces.values()] : [...gameState.playerPieces.values()];

        let bestMove = null;
        let bestScore = isMaximizing ? -Infinity : Infinity;

        // 按优先级排序移动
        const allMoves = [];
        for (const { piece, row, col } of pieces) {
            const validMoves = getValidMoves(piece, row, col);
            for (const move of validMoves) {
                const moveScore = quickEvaluateMove(piece, row, col, move.r, move.c);
                allMoves.push({ piece, fromR: row, fromC: col, toR: move.r, toC: move.c, priority: moveScore });
            }
        }

        // 按优先级排序，优先考虑高价值移动
        allMoves.sort((a, b) => isMaximizing ? b.priority - a.priority : a.priority - b.priority);

        // 限制搜索宽度以提高性能
        const maxMoves = depth > 1 ? 8 : allMoves.length;

        for (let i = 0; i < Math.min(maxMoves, allMoves.length); i++) {
            const move = allMoves[i];
            const { piece, fromR, fromC, toR, toC } = move;

            // 模拟移动
            const originalTarget = boardState[toR][toC];
            const moveResult = simulateMove(piece, fromR, fromC, toR, toC);

            if (moveResult.valid) {
                const score = minimax(depth - 1, !isMaximizing, alpha, beta).score;

                // 撤销移动
                undoSimulatedMove(moveResult);

                if (isMaximizing) {
                    if (score > bestScore) {
                        bestScore = score;
                        bestMove = move;
                    }
                    alpha = Math.max(alpha, score);
                } else {
                    if (score < bestScore) {
                        bestScore = score;
                        bestMove = move;
                    }
                    beta = Math.min(beta, score);
                }

                // Alpha-Beta剪枝
                if (beta <= alpha) {
                    break;
                }
            }
        }

        return { score: bestScore, move: bestMove };
    }

    // 快速评估移动优先级
    function quickEvaluateMove(piece, fromR, fromC, toR, toC) {
        const targetPiece = boardState[toR][toC];
        let score = 0;

        if (targetPiece) {
            if (targetPiece.name === '军旗') return 10000;
            if (canWinBattle(piece, targetPiece)) {
                score += targetPiece.rank * 10;
            } else {
                score -= piece.rank * 5;
            }
        }

        // 位置价值
        score += getPositionValue(piece, toR, toC);

        return score;
    }

    // 模拟移动
    function simulateMove(piece, fromR, fromC, toR, toC) {
        const targetPiece = boardState[toR][toC];
        const moveResult = {
            valid: true,
            piece,
            fromR, fromC, toR, toC,
            originalTarget: targetPiece,
            capturedPieces: [],
            positionUpdates: []
        };

        if (targetPiece) {
            const battleResult = simulateBattle(piece, targetPiece);
            if (battleResult.draw) {
                // 双方都被消灭
                boardState[fromR][fromC] = null;
                boardState[toR][toC] = null;
                moveResult.capturedPieces = [piece, targetPiece];
                removePieceFromGame(piece);
                removePieceFromGame(targetPiece);
            } else if (battleResult.winner === piece) {
                // 攻击方获胜
                boardState[toR][toC] = piece;
                boardState[fromR][fromC] = null;
                moveResult.capturedPieces = [targetPiece];
                removePieceFromGame(targetPiece);
                updatePiecePosition(piece, toR, toC);
                moveResult.positionUpdates.push({ piece, newR: toR, newC: toC });
            } else {
                // 防守方获胜
                boardState[fromR][fromC] = null;
                moveResult.capturedPieces = [piece];
                removePieceFromGame(piece);
            }
        } else {
            // 普通移动
            boardState[toR][toC] = piece;
            boardState[fromR][fromC] = null;
            updatePiecePosition(piece, toR, toC);
            moveResult.positionUpdates.push({ piece, newR: toR, newC: toC });
        }

        return moveResult;
    }

    // 撤销模拟移动
    function undoSimulatedMove(moveResult) {
        const { piece, fromR, fromC, toR, toC, originalTarget, capturedPieces, positionUpdates } = moveResult;

        // 恢复棋盘状态
        boardState[fromR][fromC] = piece;
        boardState[toR][toC] = originalTarget;

        // 恢复被捕获的棋子
        for (const capturedPiece of capturedPieces) {
            const pieceMap = capturedPiece.owner === PLAYER ? gameState.playerPieces : gameState.aiPieces;
            if (capturedPiece === piece) {
                pieceMap.set(capturedPiece.id, { piece: capturedPiece, row: fromR, col: fromC });
            } else if (capturedPiece === originalTarget) {
                pieceMap.set(capturedPiece.id, { piece: capturedPiece, row: toR, col: toC });
            }
        }

        // 恢复位置更新
        for (const update of positionUpdates) {
            const pieceMap = update.piece.owner === PLAYER ? gameState.playerPieces : gameState.aiPieces;
            pieceMap.set(update.piece.id, { piece: update.piece, row: fromR, col: fromC });
        }
    }

    // 模拟战斗结果
    function simulateBattle(attacker, defender) {
        if (boardSchema[defender.row] && boardSchema[defender.row][defender.col] === 2) {
            return { winner: defender, draw: false }; // 营地免疫
        }

        if (attacker.name === '炸弹' || defender.name === '炸弹') {
            return { winner: null, draw: true };
        } else if (defender.name === '地雷') {
            return { winner: attacker.name === '工兵' ? attacker : null, draw: attacker.name !== '工兵' };
        } else if (defender.name === '军旗') {
            return { winner: attacker, draw: false };
        } else if (attacker.rank > defender.rank) {
            return { winner: attacker, draw: false };
        } else if (attacker.rank === defender.rank) {
            return { winner: null, draw: true };
        } else {
            return { winner: defender, draw: false };
        }
    }

    // 判断是否能赢得战斗
    function canWinBattle(attacker, defender) {
        const result = simulateBattle(attacker, defender);
        return result.winner === attacker;
    }

    // 评估整个棋盘状态
    function evaluateBoardState() {
        let score = 0;

        // 1. 材料价值评估
        score += evaluateMaterial();

        // 2. 位置价值评估
        score += evaluatePositions();

        // 3. 战略价值评估
        score += evaluateStrategy();

        // 4. 威胁评估
        score += evaluateThreats();

        return score;
    }

    // 材料价值评估
    function evaluateMaterial() {
        let aiMaterial = 0;
        let playerMaterial = 0;

        for (const { piece } of gameState.aiPieces.values()) {
            aiMaterial += getPieceValue(piece);
        }

        for (const { piece } of gameState.playerPieces.values()) {
            playerMaterial += getPieceValue(piece);
        }

        return aiMaterial - playerMaterial;
    }

    // 获取棋子价值
    function getPieceValue(piece) {
        const baseValues = {
            '军旗': 1000, '司令': 90, '军长': 80, '师长': 70, '旅长': 60,
            '团长': 50, '营长': 40, '连长': 30, '排长': 20, '工兵': 15,
            '炸弹': 35, '地雷': 25
        };
        return baseValues[piece.name] || 0;
    }

    // 位置价值评估
    function evaluatePositions() {
        let score = 0;

        // AI棋子位置评估
        for (const { piece, row, col } of gameState.aiPieces.values()) {
            score += getPositionValue(piece, row, col);
        }

        // 玩家棋子位置评估（负值）
        for (const { piece, row, col } of gameState.playerPieces.values()) {
            score -= getPositionValue(piece, row, col);
        }

        return score;
    }

    // 获取位置价值
    function getPositionValue(piece, row, col) {
        let value = 0;

        // 基础位置价值
        if (piece.owner === AI) {
            // AI棋子越靠近玩家区域价值越高
            value += (11 - row) * 2;

            // 中央位置更有价值
            const centerDistance = Math.abs(col - 2);
            value += (2 - centerDistance) * 1.5;
        } else {
            // 玩家棋子越靠近AI区域价值越高
            value += row * 2;

            // 中央位置更有价值
            const centerDistance = Math.abs(col - 2);
            value += (2 - centerDistance) * 1.5;
        }

        // 特殊位置价值
        const cellType = boardSchema[row][col];
        if (cellType === 2) { // 营地
            if (piece.name === '军旗' || piece.name === '地雷') {
                value += 10; // 重要棋子在营地更安全
            } else {
                value += 3; // 其他棋子在营地也有防护价值
            }
        } else if (cellType === 3) { // 大本营
            if (piece.name === '军旗') {
                value += 15; // 军旗在大本营最安全
            }
        }

        // 铁路位置对工兵特别有价值
        if (piece.name === '工兵' && isRailwayPosition(row, col)) {
            value += 8;
        }

        return value;
    }

    // 判断是否为铁路位置
    function isRailwayPosition(row, col) {
        // 垂直铁路：列0和4，行1-10
        const isVerticalRail = (col === 0 || col === 4) && (row >= 1 && row <= 10);
        // 水平铁路：行1,5,6,10，列0-4
        const isHorizontalRail = (row === 1 || row === 5 || row === 6 || row === 10) && (col >= 0 && col <= 4);
        return isVerticalRail || isHorizontalRail;
    }

    // 战略价值评估
    function evaluateStrategy() {
        let score = 0;

        // 1. 军旗保护评估
        score += evaluateFlagProtection();

        // 2. 前线控制评估
        score += evaluateFrontlineControl();

        // 3. 机动性评估
        score += evaluateMobility();

        return score;
    }

    // 军旗保护评估
    function evaluateFlagProtection() {
        let score = 0;

        // 检查AI军旗周围的保护
        const aiFlag = [...gameState.aiPieces.values()].find(p => p.piece.name === '军旗');
        if (aiFlag) {
            const protectors = countProtectorsAround(aiFlag.row, aiFlag.col, AI);
            score += protectors * 5;
        }

        // 检查玩家军旗周围的威胁
        const playerFlag = [...gameState.playerPieces.values()].find(p => p.piece.name === '军旗');
        if (playerFlag) {
            const threats = countThreatsAround(playerFlag.row, playerFlag.col, AI);
            score += threats * 8;
        }

        return score;
    }

    // 计算周围的保护者数量
    function countProtectorsAround(row, col, owner) {
        let count = 0;
        const directions = [[-1,0], [1,0], [0,-1], [0,1], [-1,-1], [-1,1], [1,-1], [1,1]];

        for (const [dr, dc] of directions) {
            const nr = row + dr;
            const nc = col + dc;
            if (isValid(nr, nc)) {
                const piece = boardState[nr][nc];
                if (piece && piece.owner === owner && piece.name !== '军旗' && piece.name !== '地雷') {
                    count++;
                }
            }
        }

        return count;
    }

    // 计算周围的威胁数量
    function countThreatsAround(row, col, threatOwner) {
        let count = 0;
        const directions = [[-1,0], [1,0], [0,-1], [0,1]];

        for (const [dr, dc] of directions) {
            const nr = row + dr;
            const nc = col + dc;
            if (isValid(nr, nc)) {
                const piece = boardState[nr][nc];
                if (piece && piece.owner === threatOwner) {
                    // 检查这个棋子是否能在下一步攻击到目标位置
                    const moves = getValidMoves(piece, nr, nc);
                    if (moves.some(m => m.r === row && m.c === col)) {
                        count++;
                    }
                }
            }
        }

        return count;
    }

    // 前线控制评估
    function evaluateFrontlineControl() {
        let score = 0;
        const frontlineRows = [5, 6]; // 中线附近

        for (const row of frontlineRows) {
            for (let col = 0; col < COLS; col++) {
                const piece = boardState[row][col];
                if (piece) {
                    if (piece.owner === AI) {
                        score += 3;
                    } else {
                        score -= 3;
                    }
                }
            }
        }

        return score;
    }

    // 机动性评估
    function evaluateMobility() {
        let aiMobility = 0;
        let playerMobility = 0;

        // 计算AI的机动性
        for (const { piece, row, col } of gameState.aiPieces.values()) {
            const moves = getValidMoves(piece, row, col);
            aiMobility += moves.length;
        }

        // 计算玩家的机动性
        for (const { piece, row, col } of gameState.playerPieces.values()) {
            const moves = getValidMoves(piece, row, col);
            playerMobility += moves.length;
        }

        return (aiMobility - playerMobility) * 0.5;
    }

    // 威胁评估
    function evaluateThreats() {
        let score = 0;

        // 评估AI对玩家的威胁
        for (const { piece, row, col } of gameState.aiPieces.values()) {
            const moves = getValidMoves(piece, row, col);
            for (const move of moves) {
                const target = boardState[move.r][move.c];
                if (target && target.owner === PLAYER) {
                    if (canWinBattle(piece, target)) {
                        score += getPieceValue(target) * 0.1;

                        // 对重要棋子的威胁更有价值
                        if (target.name === '军旗') {
                            score += 50;
                        } else if (target.name === '司令' || target.name === '军长') {
                            score += 10;
                        }
                    }
                }
            }
        }

        // 评估玩家对AI的威胁（负分）
        for (const { piece, row, col } of gameState.playerPieces.values()) {
            const moves = getValidMoves(piece, row, col);
            for (const move of moves) {
                const target = boardState[move.r][move.c];
                if (target && target.owner === AI) {
                    if (canWinBattle(piece, target)) {
                        score -= getPieceValue(target) * 0.1;

                        // 对AI重要棋子的威胁需要防范
                        if (target.name === '军旗') {
                            score -= 50;
                        } else if (target.name === '司令' || target.name === '军长') {
                            score -= 10;
                        }
                    }
                }
            }
        }

        return score;
    }

    // 增强版移动评估（保留原有接口兼容性）
    function evaluateMove(piece, fromR, fromC, toR, toC) {
        let score = 0;
        const targetPiece = boardState[toR][toC];

        // 基础攻击评估
        if (targetPiece) {
            if (targetPiece.name === '军旗') return 1000;

            if (piece.name === '炸弹') {
                score += (targetPiece.rank * 3);
            } else if (targetPiece.name === '炸弹') {
                score -= 150;
            } else if (targetPiece.name === '地雷' && piece.name !== '工兵') {
                score -= 150;
            } else if (piece.rank >= targetPiece.rank) {
                score += 15 + (targetPiece.rank * 3);

                // 奖励吃掉高价值棋子
                if (targetPiece.name === '司令') score += 30;
                else if (targetPiece.name === '军长') score += 25;
                else if (targetPiece.name === '师长') score += 20;
            } else {
                score -= 15 + (piece.rank * 3);
            }
        }

        // 位置改进评估
        const oldPositionValue = getPositionValue(piece, fromR, fromC);
        const newPositionValue = getPositionValue(piece, toR, toC);
        score += (newPositionValue - oldPositionValue) * 0.5;

        // 战略位置奖励
        if (piece.owner === AI) {
            // 向前推进奖励
            if (toR > fromR) {
                score += 2;
            }

            // 控制中央奖励
            const centerDistance = Math.abs(toC - 2);
            if (centerDistance <= 1) {
                score += 1;
            }
        }

        // 防守评估
        if (piece.name === '司令' || piece.name === '军长') {
            // 重要棋子不要过于冒险
            const dangerLevel = countThreatsAround(toR, toC, PLAYER);
            score -= dangerLevel * 5;
        }

        // 添加少量随机性避免重复走法
        score += (Math.random() - 0.5) * 0.2;

        return score;
    }

    startButton.addEventListener('click', startGame);
    resetButton.addEventListener('click', resetGame);

    // 添加AI难度显示
    updateStatus("AI已增强！现在具有更强的战略思维和预测能力。", 'waiting');
});