# 暗棋模式实现

## 概述
游戏现在采用暗棋模式，玩家只能看到自己的棋子，AI的棋子保持隐藏状态，增加了游戏的策略性和挑战性。

## 核心特性

### 🔍 **棋子可见性规则**
- **玩家棋子**: 始终可见，显示真实身份
- **AI棋子**: 默认隐藏，显示为灰色问号 "?"
- **战斗揭示**: 参与战斗的AI棋子会被揭示身份
- **被吃棋子**: 被吃的棋子在俘虏区显示真实身份

### 🎯 **视觉设计**
- **玩家棋子**: 蓝色背景，显示棋子名称首字符
- **隐藏AI棋子**: 灰色背景，显示问号 "?"
- **揭示AI棋子**: 红色背景，显示棋子名称首字符
- **悬停效果**: 隐藏棋子有轻微放大效果

### ⚔️ **战斗机制**
- 当玩家攻击AI棋子时，双方棋子身份都会被揭示
- 当AI攻击玩家棋子时，AI棋子身份会被揭示
- 一旦棋子被揭示，就会保持可见状态

## 技术实现

### 1. 棋子创建逻辑
```javascript
const createPiece = (name, owner) => ({
    name,
    owner,
    rank: PIECE_RANKS[name],
    revealed: owner === PLAYER, // 只有玩家的棋子默认可见
    id: `${owner}-${name}-${Math.random().toString(36).substr(2, 5)}`
});
```

### 2. 渲染逻辑
```javascript
// 根据棋子是否被揭示来显示内容
if (piece.revealed) {
    pieceElement.textContent = piece.name === '军旗' ? '旗' : piece.name.charAt(0);
} else {
    pieceElement.textContent = '?';
    pieceElement.classList.add('hidden-piece');
}
```

### 3. 战斗揭示机制
```javascript
function resolveAttack(attacker, defender, fromR, fromC, toR, toC) {
    // 战斗时揭示参与战斗的棋子
    if (!attacker.revealed) attacker.revealed = true;
    if (!defender.revealed) defender.revealed = true;
    // ... 战斗逻辑
}
```

### 4. CSS样式
```css
/* 隐藏的AI棋子样式 */
.piece.ai.hidden-piece {
    background-color: #424242;
    color: #ffffff;
    border-color: #212121;
    font-size: 20px;
    font-weight: bold;
}

.piece.ai.hidden-piece:hover {
    background-color: #616161;
    transform: translate(-50%, -50%) scale(1.05);
}
```

## 游戏策略影响

### 🧠 **增强的策略深度**
1. **信息战**: 玩家需要通过战斗来获取AI棋子信息
2. **风险评估**: 攻击未知棋子存在风险，需要谨慎判断
3. **记忆管理**: 需要记住已揭示的AI棋子位置和身份
4. **诱敌深入**: 可以利用AI对己方棋子的不确定性

### 🎲 **随机布局 + 暗棋**
- 每局游戏AI使用不同的布局策略
- 玩家无法预测AI的棋子分布
- 增加了游戏的重玩价值和挑战性

### 🏆 **胜利条件**
- 吃掉对方军旗获胜
- 由于暗棋模式，寻找军旗变得更加困难
- 需要通过战斗逐步揭示AI棋子身份

## 用户体验

### 📱 **界面友好性**
- 清晰的视觉区分：蓝色(玩家) vs 灰色(隐藏) vs 红色(揭示)
- 悬停效果提供交互反馈
- 被吃棋子显示完整信息，便于分析

### 🎮 **游戏平衡性**
- AI仍然使用增强的Minimax算法
- AI能看到所有棋子，但玩家信息有限
- 通过随机布局平衡双方优势

### 🔄 **学习曲线**
- 新手：通过战斗学习不同棋子的特性
- 进阶：发展记忆和推理策略
- 高手：利用信息不对称制定复杂战术

这种暗棋模式大大增加了游戏的策略深度和挑战性，让每一次攻击都充满未知和刺激！
