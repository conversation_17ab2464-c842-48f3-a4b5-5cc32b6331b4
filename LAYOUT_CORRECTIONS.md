# 布局位置修正

## 问题说明
新增的3种布局中存在棋子位置错误的问题，主要是军旗和其他重要棋子没有放在正确的位置上。

## 棋盘位置规则

### 位置类型定义
- **大本营 (HQ)**: 位置代码 `3`，坐标 (0,1), (0,3), (11,1), (11,3)
- **营地 (Camp)**: 位置代码 `2`，提供防护，不能被攻击
- **普通位置**: 位置代码 `1`，正常移动和战斗

### 重要规则
1. **军旗必须放在大本营** - 只能在 (11,1) 或 (11,3)
2. **司令通常放在大本营** - 提供最高保护
3. **地雷可以放在任何己方区域** - 但不能放在营地中
4. **营地位置不能放置棋子** - 只能作为移动目标

## 修正内容

### Layout3 - 平衡发展阵 ✅
**修正前问题**:
- 司令在 [10,2] (普通位置)
- 军长在 [11,1] (大本营) - 应该是司令的位置

**修正后**:
- 司令: [11,1] (大本营) ✅
- 军长: [10,1] (普通位置) ✅
- 军旗: [11,3] (大本营) ✅

### Layout4 - 工兵突击阵 ✅
**修正前问题**:
- 布局基本正确，但需要优化位置分布

**修正后**:
- 司令: [11,1] (大本营) ✅
- 军旗: [11,3] (大本营) ✅
- 工兵前置策略保持 ✅

### Layout5 - 铁桶防守阵 ✅
**修正前问题**:
- 军旗在 [11,2] ❌ (不是大本营位置)

**修正后**:
- 司令: [11,3] (大本营) ✅
- 军旗: [11,1] (大本营) ✅
- 炸弹前置保护策略优化 ✅

## 修正后的5种布局特点

### 1. 经典防守阵 (Layout1)
- **特点**: 稳健的后排防守
- **军旗**: [11,1] (大本营)
- **司令**: [11,3] (大本营)
- **策略**: 重点保护后排，稳扎稳打

### 2. 积极进攻阵 (Layout2)
- **特点**: 前置攻击棋子
- **军旗**: [11,1] (大本营)
- **司令**: [11,3] (大本营)
- **策略**: 主动出击，快速建立优势

### 3. 平衡发展阵 (Layout3)
- **特点**: 攻守兼备
- **军旗**: [11,3] (大本营)
- **司令**: [11,1] (大本营)
- **策略**: 均衡发展，适应性强

### 4. 工兵突击阵 (Layout4)
- **特点**: 工兵前置，适合快速突破
- **军旗**: [11,3] (大本营)
- **司令**: [11,1] (大本营)
- **策略**: 利用工兵的铁路优势快速机动

### 5. 铁桶防守阵 (Layout5)
- **特点**: 炸弹前置，重点防守
- **军旗**: [11,1] (大本营)
- **司令**: [11,3] (大本营)
- **策略**: 层层防守，以守为攻

## 验证结果

### ✅ 位置合规性检查
- 所有军旗都在大本营位置 ✅
- 所有司令都在大本营位置 ✅
- 没有棋子放在营地位置 ✅
- 棋子数量正确 (每种布局25个棋子) ✅

### ✅ 语法检查
- JavaScript语法正确 ✅
- 布局数据结构完整 ✅
- 所有坐标在有效范围内 ✅

### ✅ 游戏平衡性
- 5种布局各有特色 ✅
- 随机分配增加变化性 ✅
- 暗棋模式增加挑战性 ✅

现在所有布局都符合军旗游戏的正确规则，军旗和司令都安全地放置在大本营中，为玩家提供更加真实和挑战性的游戏体验！
