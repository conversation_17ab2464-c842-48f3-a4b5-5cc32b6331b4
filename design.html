<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>军旗游戏</title>
<script src="https://cdn.tailwindcss.com"></script>
<link href="https://fonts.googleapis.com/css2?family=Material+Icons" rel="stylesheet"/>
</head>
<body class="bg-gray-100 flex items-center justify-center h-screen">
<div class="flex gap-8 p-8 bg-white shadow-lg rounded-lg">
<div class="w-[600px] h-[750px] border border-gray-300">
<img alt="Chinese chess board" class="w-full h-full object-cover" src="./棋盘.jpeg"/>
</div>
<div class="w-[320px] flex flex-col gap-6">
<h1 class="text-2xl font-bold text-gray-800 text-center">游戏控制</h1>
<div class="bg-gray-50 p-6 rounded-lg shadow-sm">
<div class="mb-4">
<label class="block text-sm font-medium text-gray-700 mb-2" for="layout">选择布局:</label>
<div class="relative">
<select class="w-full bg-white border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 appearance-none" id="layout">
<option selected="">防守反击阵</option>
<option>进攻阵</option>
<option>均衡阵</option>
</select>
<div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
<span class="material-icons">expand_more</span>
</div>
</div>
</div>
<div class="flex flex-col gap-3">
<button class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-4 rounded-lg transition duration-300 ease-in-out transform hover:scale-105">
                        开始游戏
                    </button>
<button class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition duration-300 ease-in-out transform hover:scale-105">
                        重新开始
                    </button>
</div>
</div>
<div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 rounded-r-lg" role="alert">
<p class="font-bold">恭喜!</p>
<p>你赢了！你扛了对方的军旗！</p>
</div>
<div class="bg-white p-6 rounded-lg shadow-md border border-gray-200">
<h2 class="text-lg font-semibold text-gray-800 mb-4">被吃掉的棋子</h2>
<div>
<h3 class="text-sm font-medium text-gray-600 mb-2">你:</h3>
<div class="flex flex-wrap gap-2">
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">营</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">连</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">排</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">连</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">连</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">排</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">师</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">工</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">兵</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">军</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">旅</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">师</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">炸</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">工</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">司</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">工</span>
<span class="bg-red-500 text-white text-xs font-semibold px-2.5 py-1.5 rounded">兵</span>
</div>
</div>
<div class="mt-4 pt-4 border-t border-gray-200">
<h3 class="text-sm font-medium text-gray-600 mb-2">AI:</h3>
<div class="flex flex-wrap gap-2">
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">营</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">连</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">连</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">排</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">师</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">地</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">排</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">营</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">炸</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">工</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">司</span>
<span class="bg-blue-600 text-white text-xs font-semibold px-2.5 py-1.5 rounded">工</span>
</div>
</div>
</div>
</div>
</div>

</body></html>