# 军旗游戏 AI 增强说明

## 概述
本次更新大幅增强了军旗游戏中AI的智能水平，从简单的贪心算法升级为具有深度搜索和战略思维的高级AI系统。

## 主要增强功能

### 1. Minimax算法与Alpha-Beta剪枝
- **搜索深度**: 3层深度搜索，能预测未来3步走法
- **Alpha-Beta剪枝**: 大幅提高搜索效率，减少不必要的计算
- **移动排序**: 优先评估高价值移动，提高剪枝效果

### 2. 全面的棋盘状态评估
AI现在会从多个维度评估棋盘状态：

#### 材料价值评估
- 精确计算双方棋子的总价值
- 不同棋子有不同的基础价值权重
- 司令(90分) > 军长(80分) > 师长(70分) > ... > 工兵(15分)

#### 位置价值评估
- **前进奖励**: 棋子越接近敌方区域价值越高
- **中央控制**: 中央位置的棋子更有战略价值
- **特殊位置**: 营地和大本营提供额外的安全价值
- **铁路优势**: 工兵在铁路位置获得额外价值

#### 战略价值评估
- **军旗保护**: 评估军旗周围的保护程度
- **前线控制**: 控制中线区域的重要性
- **机动性**: 棋子的可移动选项数量

#### 威胁评估
- **攻击机会**: 识别可以攻击敌方棋子的机会
- **防守需求**: 识别需要保护的己方棋子
- **重要目标**: 对司令、军长等高价值目标的特殊关注

### 3. 智能移动评估
- **战斗结果预测**: 准确预测攻击结果
- **位置改进**: 评估移动后位置的改善程度
- **风险评估**: 重要棋子避免过度冒险
- **战略定位**: 优先占据有利位置

### 4. 高级战术特性
- **移动模拟**: 完整模拟移动和战斗结果
- **状态回滚**: 准确撤销模拟移动
- **优先级排序**: 智能排序候选移动
- **搜索宽度控制**: 平衡搜索深度和计算效率

## 性能优化

### 搜索优化
- 限制搜索宽度避免组合爆炸
- 按优先级排序减少搜索时间
- Alpha-Beta剪枝提高效率

### 计算优化
- 快速移动评估用于初步筛选
- 缓存重复计算结果
- 智能的搜索终止条件

## AI难度提升

### 之前的AI特点
- 只看一步走法
- 简单的贪心策略
- 缺乏战略规划
- 容易被预测

### 增强后的AI特点
- 3步深度预测
- 复杂的多维度评估
- 战略性思维
- 适应性强，难以预测

## 游戏体验改进

### 更具挑战性
- AI能够制定长期战略
- 更好的防守和攻击协调
- 减少明显的错误走法

### 更真实的对战
- AI会保护重要棋子
- 合理的风险评估
- 更像人类玩家的思维模式

### 动态难度
- AI会根据局面调整策略
- 在优势时稳健，劣势时冒险
- 保持游戏的紧张感和不确定性

## 技术实现细节

### 核心算法
```javascript
// Minimax with Alpha-Beta Pruning
function minimax(depth, isMaximizing, alpha, beta) {
    // 递归搜索最优走法
    // Alpha-Beta剪枝优化
    // 返回最佳评分和对应走法
}
```

### 评估函数
```javascript
function evaluateBoardState() {
    return evaluateMaterial() + 
           evaluatePositions() + 
           evaluateStrategy() + 
           evaluateThreats();
}
```

### 移动模拟
```javascript
function simulateMove(piece, fromR, fromC, toR, toC) {
    // 完整模拟移动过程
    // 处理战斗结果
    // 支持完整回滚
}
```

## 使用说明

1. 打开游戏页面
2. 选择布局（经典攻击阵或防守反击阵）
3. 点击"开始游戏"
4. 享受与增强AI的对战！

增强后的AI将为您提供更具挑战性和趣味性的游戏体验。祝您游戏愉快！
